// Firebase Configuration for Web Platform
// This file contains Firebase configuration for Apple Sign-In web authentication

// Firebase configuration object
const firebaseConfig = {
  apiKey: "AIzaSyA3mIlC6O1_tGeH7OpuZmEcDYSDPcNcMqo",
  authDomain: "bloomg-flutter.firebaseapp.com",
  projectId: "bloomg-flutter",
  storageBucket: "bloomg-flutter.appspot.com",
  messagingSenderId: "1068175978703",
  appId: "1:1068175978703:web:your-web-app-id"
};

// Apple Sign-In configuration
const appleSignInConfig = {
  clientId: "com.algomash.radiance",
  scope: "name email",
  redirectURI: "https://bloomg-flutter.firebaseapp.com/__/auth/handler",
  state: "origin:web",
  usePopup: true
};

// Export configurations for use in Flutter web app
window.firebaseConfig = firebaseConfig;
window.appleSignInConfig = appleSignInConfig;

// Initialize Firebase (if needed for direct web usage)
if (typeof firebase !== 'undefined') {
  firebase.initializeApp(firebaseConfig);
}

console.log('Firebase and Apple Sign-In configurations loaded');
